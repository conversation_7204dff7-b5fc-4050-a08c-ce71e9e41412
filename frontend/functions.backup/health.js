/**
 * Cloudflare Pages Function for Health Check Proxy
 * 
 * 这个函数将 /health 请求代理到后端 Workers
 */

const BACKEND_API_URL = 'https://college-employment-survey.aibook2099.workers.dev';

export async function onRequest(context) {
  const { request } = context;
  
  // 构建后端API URL
  const backendUrl = `${BACKEND_API_URL}/health`;
  
  console.log(`Proxying health check: /health -> ${backendUrl}`);
  
  try {
    // 发送请求到后端
    const response = await fetch(backendUrl, {
      method: request.method,
      headers: request.headers,
    });
    
    // 创建新的响应，添加CORS头部
    const newResponse = new Response(response.body, {
      status: response.status,
      statusText: response.statusText,
      headers: {
        ...Object.fromEntries(response.headers),
        'Access-Control-Allow-Origin': '*',
        'Access-Control-Allow-Methods': 'GET, POST, PUT, DELETE, OPTIONS',
        'Access-Control-Allow-Headers': 'Content-Type, Authorization',
      },
    });
    
    return newResponse;
  } catch (error) {
    console.error('Health check proxy error:', error);
    
    return new Response(JSON.stringify({
      success: false,
      error: '健康检查代理错误',
      message: error.message,
      timestamp: new Date().toISOString()
    }), {
      status: 500,
      headers: {
        'Content-Type': 'application/json',
        'Access-Control-Allow-Origin': '*',
      },
    });
  }
}

/**
 * Cloudflare Pages Function for API Proxy
 * 
 * 这个函数将所有 /api/* 请求代理到后端 Workers
 */

const BACKEND_API_URL = 'https://college-employment-survey.aibook2099.workers.dev';

export async function onRequest(context) {
  const { request } = context;
  const url = new URL(request.url);
  
  // 构建后端API URL
  const backendUrl = `${BACKEND_API_URL}${url.pathname}${url.search}`;
  
  console.log(`Proxying request: ${url.pathname} -> ${backendUrl}`);
  
  try {
    // 创建新的请求，保持原始的方法、头部和body
    const proxyRequest = new Request(backendUrl, {
      method: request.method,
      headers: request.headers,
      body: request.body,
    });
    
    // 发送请求到后端
    const response = await fetch(proxyRequest);
    
    // 创建新的响应，添加CORS头部
    const newResponse = new Response(response.body, {
      status: response.status,
      statusText: response.statusText,
      headers: {
        ...Object.fromEntries(response.headers),
        'Access-Control-Allow-Origin': '*',
        'Access-Control-Allow-Methods': 'GET, POST, PUT, DELETE, OPTIONS',
        'Access-Control-Allow-Headers': 'Content-Type, Authorization',
      },
    });
    
    return newResponse;
  } catch (error) {
    console.error('Proxy error:', error);
    
    return new Response(JSON.stringify({
      success: false,
      error: 'API代理错误',
      message: error.message,
      timestamp: new Date().toISOString()
    }), {
      status: 500,
      headers: {
        'Content-Type': 'application/json',
        'Access-Control-Allow-Origin': '*',
      },
    });
  }
}

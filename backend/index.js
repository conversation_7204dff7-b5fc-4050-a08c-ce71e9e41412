/**
 * V2 Entry point for Cloudflare Workers
 * 统一的API架构，解决V1的混乱问题
 */

import { Hono } from 'hono';
import { cors } from 'hono/cors';
import { logger } from 'hono/logger';
import { secureHeaders } from 'hono/secure-headers';
import { DatabaseService } from './database-service.js';
import { ReviewService } from './review-service.js';
import { MonitoringService } from './monitoring-service.js';

const app = new Hono();

// 基础中间件
app.use('*', logger());
app.use('*', secureHeaders());
app.use('*', cors({
  origin: [
    'http://localhost:5173',
    'http://localhost:5174',
    'https://college-employment-survey-v2.pages.dev'
  ],
  allowMethods: ['GET', 'POST', 'PUT', 'DELETE', 'OPTIONS'],
  allowHeaders: ['Content-Type', 'Authorization', 'X-Test-Robot', 'X-Request-ID'],
  credentials: true
}));

// 监控中间件
app.use('*', async (c, next) => {
  const startTime = Date.now();
  const isTestRobot = c.req.header('X-Test-Robot') === 'true';

  try {
    await next();

    // 记录成功的请求指标
    const responseTime = Date.now() - startTime;
    const monitoringService = new MonitoringService(c.env.DB, c.env.SURVEY_KV);

    // 异步记录指标，不阻塞响应
    c.executionCtx.waitUntil(
      monitoringService.recordApiMetrics(
        c.req.path,
        c.req.method,
        responseTime,
        c.res.status,
        isTestRobot
      )
    );
  } catch (error) {
    // 记录错误指标
    const responseTime = Date.now() - startTime;
    const monitoringService = new MonitoringService(c.env.DB, c.env.SURVEY_KV);

    c.executionCtx.waitUntil(
      monitoringService.recordError(error, {
        endpoint: c.req.path,
        method: c.req.method,
        userAgent: c.req.header('User-Agent'),
        isTestRobot
      })
    );

    throw error;
  }
});

// V2 API健康检查
app.get('/api/health', async (c) => {
  try {
    // 初始化数据库服务
    const dbService = new DatabaseService(c.env.DB);

    // 测试数据库连接
    const dbHealth = await dbService.healthCheck();

    return c.json({
      success: true,
      data: {
        status: 'healthy',
        version: '2.0.0',
        timestamp: new Date().toISOString(),
        database: {
          connected: dbHealth.connected,
          status: dbHealth.connected ? 'healthy' : 'unhealthy',
          service: 'DatabaseService V2'
        },
        environment: c.env.ENVIRONMENT || 'development'
      },
      message: 'V2 API is running'
    });
  } catch (error) {
    return c.json({
      success: false,
      data: {
        status: 'unhealthy',
        error: error.message,
        timestamp: new Date().toISOString()
      },
      error: 'Health check failed'
    }, 500);
  }
});

// V2 API版本信息
app.get('/api/version', async (c) => {
  return c.json({
    success: true,
    data: {
      version: '2.0.0',
      name: 'College Employment Survey V2',
      description: 'Refactored architecture with unified database',
      features: [
        'Unified D1 database architecture',
        'Complete TypeScript type safety',
        'Integrated review system',
        'Real-time data processing',
        'Enhanced security'
      ],
      timestamp: new Date().toISOString()
    }
  });
});

// V2问卷提交API (兼容测试机器人)
app.post('/api/questionnaire/submit', async (c) => {
  try {
    const body = await c.req.json();
    const isTestRobot = c.req.header('X-Test-Robot') === 'true';
    const requestId = c.req.header('X-Request-ID') || `req_${Date.now()}`;

    console.log(`[${requestId}] 收到问卷提交请求 (测试机器人: ${isTestRobot})`);

    // 简单的数据验证
    if (!body.educationLevel && !body.major) {
      return c.json({
        success: false,
        error: 'Missing required fields'
      }, 400);
    }

    // 初始化数据库服务
    const dbService = new DatabaseService(c.env.DB);

    // 插入问卷数据到D1数据库
    const result = await dbService.insertQuestionnaireResponse(body, isTestRobot);

    if (result.success) {
      console.log(`[${requestId}] 问卷数据插入成功:`, result.data);

      return c.json({
        success: true,
        data: {
          id: result.data.id,
          sequenceNumber: result.data.sequenceNumber,
          status: 'approved', // 问卷直接通过
          message: '提交成功，已直接发布'
        },
        message: 'Questionnaire submitted successfully'
      });
    } else {
      console.error(`[${requestId}] 问卷数据插入失败:`, result.error);

      return c.json({
        success: false,
        error: result.error || 'Failed to save questionnaire'
      }, 500);
    }
  } catch (error) {
    console.error('问卷提交错误:', error);
    return c.json({
      success: false,
      error: 'Failed to submit questionnaire'
    }, 500);
  }
});

// V2故事提交API (兼容测试机器人)
app.post('/api/story/submit', async (c) => {
  try {
    const body = await c.req.json();
    const isTestRobot = c.req.header('X-Test-Robot') === 'true';
    const requestId = c.req.header('X-Request-ID') || `req_${Date.now()}`;

    console.log(`[${requestId}] 收到故事提交请求 (测试机器人: ${isTestRobot})`);

    // 简单的数据验证
    if (!body.title || !body.content) {
      return c.json({
        success: false,
        error: 'Missing required fields: title and content'
      }, 400);
    }

    // 初始化数据库服务
    const dbService = new DatabaseService(c.env.DB);

    // 插入故事数据到D1数据库
    const result = await dbService.insertStory(body, isTestRobot);

    if (result.success) {
      console.log(`[${requestId}] 故事数据插入成功:`, result.data);

      return c.json({
        success: true,
        data: {
          id: result.data.id,
          sequenceNumber: result.data.sequenceNumber,
          title: body.title,
          status: result.data.status,
          bypassReview: result.data.bypassReview,
          message: result.data.bypassReview ? '故事提交成功，已直接发布' : '故事提交成功，等待审核'
        },
        message: 'Story submitted successfully'
      });
    } else {
      console.error(`[${requestId}] 故事数据插入失败:`, result.error);

      return c.json({
        success: false,
        error: result.error || 'Failed to save story'
      }, 500);
    }
  } catch (error) {
    console.error('故事提交错误:', error);
    return c.json({
      success: false,
      error: 'Failed to submit story'
    }, 500);
  }
});

// V2统计数据API
app.get('/api/questionnaire/stats', async (c) => {
  try {
    // 初始化数据库服务
    const dbService = new DatabaseService(c.env.DB);

    // 从D1数据库获取真实统计数据
    const result = await dbService.getStatistics();

    if (result.success) {
      console.log('统计数据获取成功:', result.data);

      // 转换为前端期望的格式
      const transformedData = {
        success: true,
        statistics: {
          totalResponses: result.data.total || 0,
          verifiedCount: Math.floor((result.data.total || 0) * 0.7), // 估算已验证数量
          anonymousCount: Math.floor((result.data.total || 0) * 0.3), // 估算匿名数量
          educationLevels: result.data.educationLevels || [],
          regions: result.data.regions || [],
          majors: result.data.majors || [],
          industries: [], // 暂时为空
          employmentStatus: result.data.employmentStatus || [],
          graduationYears: result.data.graduationYears || [],
          salaryRanges: [] // 暂时为空
        },
        metadata: {
          lastUpdated: new Date().toISOString(),
          dataVersion: '2.0.0',
          cacheExpiry: 30000
        }
      };

      return c.json(transformedData);
    } else {
      console.error('统计数据获取失败:', result.error);

      // 返回默认数据作为后备
      const fallbackStats = {
        success: true,
        statistics: {
          totalResponses: 0,
          verifiedCount: 0,
          anonymousCount: 0,
          educationLevels: [],
          regions: [],
          majors: [],
          industries: [],
          employmentStatus: [],
          graduationYears: [],
          salaryRanges: []
        },
        metadata: {
          lastUpdated: new Date().toISOString(),
          dataVersion: '2.0.0',
          cacheExpiry: 30000,
          warning: result.error
        }
      };

      return c.json(fallbackStats);
    }
  } catch (error) {
    console.error('统计数据错误:', error);
    return c.json({
      success: false,
      error: 'Failed to fetch statistics'
    }, 500);
  }
});

// V2统计数据API (详细版本)
app.get('/api/questionnaire/stats/overview', async (c) => {
  try {
    // 初始化数据库服务
    const dbService = new DatabaseService(c.env.DB);

    // 从D1数据库获取真实统计数据
    const result = await dbService.getStatistics();

    if (result.success) {
      console.log('统计数据获取成功:', result.data);

      return c.json({
        success: true,
        data: result.data,
        message: 'Statistics retrieved successfully',
        source: 'D1 Database'
      });
    } else {
      console.error('统计数据获取失败:', result.error);

      // 返回默认数据作为后备
      const fallbackStats = {
        total: 0,
        educationLevels: [],
        regions: [],
        employmentStatus: []
      };

      return c.json({
        success: true,
        data: fallbackStats,
        message: 'Statistics retrieved (fallback data)',
        source: 'Fallback',
        warning: result.error
      });
    }
  } catch (error) {
    console.error('统计数据错误:', error);
    return c.json({
      success: false,
      error: 'Failed to fetch statistics'
    }, 500);
  }
});

// V2审核系统API

// 获取待审核内容
app.get('/api/review/pending', async (c) => {
  try {
    const reviewService = new ReviewService(c.env.DB);

    const page = parseInt(c.req.query('page') || '1');
    const pageSize = parseInt(c.req.query('pageSize') || '20');
    const type = c.req.query('type') || null;

    const result = await reviewService.getPendingContent({
      page,
      pageSize,
      type
    });

    return c.json({
      success: result.success,
      data: result.data,
      message: result.success ? 'Pending content retrieved successfully' : result.error
    });
  } catch (error) {
    console.error('获取待审核内容失败:', error);
    return c.json({
      success: false,
      error: 'Failed to fetch pending content'
    }, 500);
  }
});

// 审核内容
app.post('/api/review/content/:contentId', async (c) => {
  try {
    const reviewService = new ReviewService(c.env.DB);

    const contentId = c.req.param('contentId');
    const body = await c.req.json();
    const { action, reviewerId, notes = '' } = body;

    if (!action || !reviewerId) {
      return c.json({
        success: false,
        error: 'Missing required fields: action, reviewerId'
      }, 400);
    }

    const result = await reviewService.reviewContent(contentId, action, reviewerId, notes);

    return c.json({
      success: result.success,
      data: result.data,
      message: result.success ? 'Content reviewed successfully' : result.error
    });
  } catch (error) {
    console.error('审核内容失败:', error);
    return c.json({
      success: false,
      error: 'Failed to review content'
    }, 500);
  }
});

// 批量审核
app.post('/api/review/batch', async (c) => {
  try {
    const reviewService = new ReviewService(c.env.DB);

    const body = await c.req.json();
    const { contentIds, action, reviewerId, notes = '' } = body;

    if (!contentIds || !Array.isArray(contentIds) || !action || !reviewerId) {
      return c.json({
        success: false,
        error: 'Missing required fields: contentIds (array), action, reviewerId'
      }, 400);
    }

    const result = await reviewService.batchReview(contentIds, action, reviewerId, notes);

    return c.json({
      success: result.success,
      data: result.data,
      message: result.success ? 'Batch review completed' : result.error
    });
  } catch (error) {
    console.error('批量审核失败:', error);
    return c.json({
      success: false,
      error: 'Failed to batch review'
    }, 500);
  }
});

// 获取审核统计
app.get('/api/review/stats', async (c) => {
  try {
    const reviewService = new ReviewService(c.env.DB);

    const reviewerId = c.req.query('reviewerId') || null;

    const result = await reviewService.getReviewStats(reviewerId);

    return c.json({
      success: result.success,
      data: result.data,
      message: result.success ? 'Review stats retrieved successfully' : result.error
    });
  } catch (error) {
    console.error('获取审核统计失败:', error);
    return c.json({
      success: false,
      error: 'Failed to fetch review stats'
    }, 500);
  }
});

// 获取审核历史
app.get('/api/review/history', async (c) => {
  try {
    const reviewService = new ReviewService(c.env.DB);

    const page = parseInt(c.req.query('page') || '1');
    const pageSize = parseInt(c.req.query('pageSize') || '20');
    const reviewerId = c.req.query('reviewerId') || null;
    const action = c.req.query('action') || null;
    const startDate = c.req.query('startDate') || null;
    const endDate = c.req.query('endDate') || null;

    const result = await reviewService.getReviewHistory({
      page,
      pageSize,
      reviewerId,
      action,
      startDate,
      endDate
    });

    return c.json({
      success: result.success,
      data: result.data,
      message: result.success ? 'Review history retrieved successfully' : result.error
    });
  } catch (error) {
    console.error('获取审核历史失败:', error);
    return c.json({
      success: false,
      error: 'Failed to fetch review history'
    }, 500);
  }
});

// V2故事列表API
app.get('/api/story/list', async (c) => {
  try {
    // 初始化数据库服务
    const dbService = new DatabaseService(c.env.DB);

    // 获取查询参数
    const page = parseInt(c.req.query('page') || '1');
    const pageSize = parseInt(c.req.query('pageSize') || '6');
    const sortBy = c.req.query('sortBy') || 'latest';
    const category = c.req.query('category');
    const tag = c.req.query('tag');
    const search = c.req.query('search');

    console.log('获取故事列表:', { page, pageSize, sortBy, category, tag, search });

    // 从D1数据库获取故事列表
    const result = await dbService.getStoryList({
      page,
      pageSize,
      sortBy,
      category,
      tag,
      search
    });

    if (result.success) {
      console.log('故事列表获取成功:', result.data);

      return c.json({
        success: true,
        data: result.data.stories || [],
        pagination: {
          page,
          pageSize,
          total: result.data.total || 0,
          totalPages: Math.ceil((result.data.total || 0) / pageSize)
        },
        message: 'Stories retrieved successfully'
      });
    } else {
      console.error('故事列表获取失败:', result.error);

      // 返回空列表作为后备
      return c.json({
        success: true,
        data: [],
        pagination: {
          page,
          pageSize,
          total: 0,
          totalPages: 0
        },
        message: 'No stories found',
        warning: result.error
      });
    }
  } catch (error) {
    console.error('故事列表错误:', error);
    return c.json({
      success: false,
      error: 'Failed to fetch story list'
    }, 500);
  }
});

// V2故事详情API
app.get('/api/story/detail/:id', async (c) => {
  try {
    const storyId = c.req.param('id');

    // 初始化数据库服务
    const dbService = new DatabaseService(c.env.DB);

    // 从D1数据库获取故事详情
    const result = await dbService.getStoryDetail(storyId);

    if (result.success && result.data) {
      console.log('故事详情获取成功:', result.data);

      return c.json({
        success: true,
        data: result.data,
        message: 'Story detail retrieved successfully'
      });
    } else {
      console.error('故事详情获取失败:', result.error);

      return c.json({
        success: false,
        error: 'Story not found'
      }, 404);
    }
  } catch (error) {
    console.error('故事详情错误:', error);
    return c.json({
      success: false,
      error: 'Failed to fetch story detail'
    }, 500);
  }
});

// V2故事投票API
app.post('/api/story/vote', async (c) => {
  try {
    const body = await c.req.json();
    const { storyId, voteType } = body;

    if (!storyId || !voteType) {
      return c.json({
        success: false,
        error: 'Missing required fields: storyId, voteType'
      }, 400);
    }

    // 初始化数据库服务
    const dbService = new DatabaseService(c.env.DB);

    // 处理投票
    const result = await dbService.voteStory(storyId, voteType);

    if (result.success) {
      console.log('故事投票成功:', result.data);

      return c.json({
        success: true,
        data: result.data,
        message: 'Vote recorded successfully'
      });
    } else {
      console.error('故事投票失败:', result.error);

      return c.json({
        success: false,
        error: result.error || 'Failed to record vote'
      }, 500);
    }
  } catch (error) {
    console.error('故事投票错误:', error);
    return c.json({
      success: false,
      error: 'Failed to process vote'
    }, 500);
  }
});

// V2监控系统API

// 获取系统性能指标
app.get('/api/monitoring/metrics', async (c) => {
  try {
    const monitoringService = new MonitoringService(c.env.DB, c.env.SURVEY_KV);

    const result = await monitoringService.getSystemMetrics();

    return c.json({
      success: result.success,
      data: result.data,
      message: result.success ? 'System metrics retrieved successfully' : result.error
    });
  } catch (error) {
    console.error('获取系统指标失败:', error);
    return c.json({
      success: false,
      error: 'Failed to fetch system metrics'
    }, 500);
  }
});

// 获取端点性能统计
app.get('/api/monitoring/endpoints', async (c) => {
  try {
    const monitoringService = new MonitoringService(c.env.DB, c.env.SURVEY_KV);

    const date = c.req.query('date') || null;
    const result = await monitoringService.getEndpointStats(date);

    return c.json({
      success: result.success,
      data: result.data,
      message: result.success ? 'Endpoint stats retrieved successfully' : result.error
    });
  } catch (error) {
    console.error('获取端点统计失败:', error);
    return c.json({
      success: false,
      error: 'Failed to fetch endpoint stats'
    }, 500);
  }
});

// 监控健康检查
app.get('/api/monitoring/health', async (c) => {
  try {
    const monitoringService = new MonitoringService(c.env.DB, c.env.SURVEY_KV);

    const result = await monitoringService.healthCheck();

    return c.json({
      success: result.success,
      data: result.data,
      message: result.success ? 'Monitoring health check passed' : result.error
    });
  } catch (error) {
    console.error('监控健康检查失败:', error);
    return c.json({
      success: false,
      error: 'Monitoring health check failed'
    }, 500);
  }
});

// 根路径
app.get('/', (c) => {
  return c.json({
    name: 'College Employment Survey V2 API',
    version: '2.0.0',
    description: 'Refactored architecture with unified database and review system',
    endpoints: {
      health: '/api/health',
      version: '/api/version',
      questionnaireSubmit: '/api/questionnaire/submit',
      storySubmit: '/api/story/submit',
      stats: '/api/questionnaire/stats',
      statsOverview: '/api/questionnaire/stats/overview',
      reviewPending: '/api/review/pending',
      reviewContent: '/api/review/content/:contentId',
      reviewBatch: '/api/review/batch',
      reviewStats: '/api/review/stats',
      reviewHistory: '/api/review/history',
      monitoringMetrics: '/api/monitoring/metrics',
      monitoringEndpoints: '/api/monitoring/endpoints',
      monitoringHealth: '/api/monitoring/health'
    },
    documentation: 'https://github.com/Aibook2099/jiuye',
    timestamp: new Date().toISOString()
  });
});

// 404处理
app.notFound((c) => {
  return c.json({
    success: false,
    error: 'API endpoint not found',
    message: `${c.req.method} ${c.req.path} is not available in V2 API`,
    timestamp: new Date().toISOString()
  }, 404);
});

export default app;

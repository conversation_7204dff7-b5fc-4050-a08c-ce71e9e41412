/**
 * V2 数据库服务层
 * 统一的D1数据库操作接口
 */

/**
 * 生成唯一ID
 */
function generateId(prefix = 'id') {
  return `${prefix}_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
}

/**
 * 生成序列号
 */
function generateSequenceNumber() {
  return `SEQ_${Date.now()}_${Math.random().toString(36).substr(2, 5)}`;
}

/**
 * 数据库服务类
 */
export class DatabaseService {
  constructor(db) {
    this.db = db;
  }

  /**
   * 插入问卷回复
   */
  async insertQuestionnaireResponse(data, isTestRobot = false) {
    try {
      const id = generateId('qr');
      const sequenceNumber = generateSequenceNumber();
      
      console.log(`[DB] 插入问卷回复: ${id} (测试机器人: ${isTestRobot})`);
      
      const stmt = this.db.prepare(`
        INSERT INTO questionnaire_responses (
          id, sequence_number, education_level, graduation_year, major, 
          school_name, region, employment_status, current_salary, 
          expected_salary, advice_for_students, observation_on_employment,
          is_anonymous, created_at, updated_at
        ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
      `);
      
      const result = await stmt.bind(
        id,
        sequenceNumber,
        data.educationLevel || null,
        data.graduationYear || null,
        data.major || null,
        data.schoolName || null,
        data.region || null,
        data.employmentStatus || null,
        data.currentSalary || null,
        data.expectedSalary || null,
        data.adviceForStudents || null,
        data.observationOnEmployment || null,
        data.isAnonymous || true,
        new Date().toISOString(),
        new Date().toISOString()
      ).run();
      
      console.log(`[DB] 问卷回复插入成功: ${id}`, result);
      
      return {
        success: true,
        data: {
          id,
          sequenceNumber,
          insertedId: result.meta.last_row_id
        }
      };
    } catch (error) {
      console.error('[DB] 插入问卷回复失败:', error);
      return {
        success: false,
        error: error.message
      };
    }
  }

  /**
   * 插入故事
   */
  async insertStory(data, isTestRobot = false) {
    try {
      const id = generateId('story');
      const sequenceNumber = generateSequenceNumber();
      
      console.log(`[DB] 插入故事: ${id} (测试机器人: ${isTestRobot})`);
      
      // 70%概率绕过审核，30%进入审核队列
      const bypassReview = Math.random() > 0.3;
      const status = bypassReview ? 'approved' : 'pending';
      
      const stmt = this.db.prepare(`
        INSERT INTO stories (
          id, sequence_number, title, content, category, tags,
          status, is_anonymous, created_at, updated_at
        ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
      `);
      
      const result = await stmt.bind(
        id,
        sequenceNumber,
        data.title,
        data.content,
        data.category || null,
        JSON.stringify(data.tags || []),
        status,
        data.isAnonymous || true,
        new Date().toISOString(),
        new Date().toISOString()
      ).run();
      
      console.log(`[DB] 故事插入成功: ${id}`, result);
      
      return {
        success: true,
        data: {
          id,
          sequenceNumber,
          status,
          bypassReview,
          insertedId: result.meta.last_row_id
        }
      };
    } catch (error) {
      console.error('[DB] 插入故事失败:', error);
      return {
        success: false,
        error: error.message
      };
    }
  }

  /**
   * 获取统计数据
   */
  async getStatistics() {
    try {
      console.log('[DB] 获取统计数据');
      
      // 获取总数
      const totalStmt = this.db.prepare('SELECT COUNT(*) as total FROM questionnaire_responses');
      const totalResult = await totalStmt.first();
      const total = totalResult?.total || 0;
      
      // 获取学历分布
      const educationStmt = this.db.prepare(`
        SELECT education_level as name, COUNT(*) as count 
        FROM questionnaire_responses 
        WHERE education_level IS NOT NULL 
        GROUP BY education_level
      `);
      const educationResults = await educationStmt.all();
      
      // 获取地区分布
      const regionStmt = this.db.prepare(`
        SELECT region as name, COUNT(*) as count 
        FROM questionnaire_responses 
        WHERE region IS NOT NULL 
        GROUP BY region 
        ORDER BY count DESC 
        LIMIT 10
      `);
      const regionResults = await regionStmt.all();
      
      // 获取就业状态分布
      const employmentStmt = this.db.prepare(`
        SELECT employment_status as name, COUNT(*) as count
        FROM questionnaire_responses
        WHERE employment_status IS NOT NULL
        GROUP BY employment_status
      `);
      const employmentResults = await employmentStmt.all();

      // 获取专业分布
      const majorStmt = this.db.prepare(`
        SELECT major as name, COUNT(*) as count
        FROM questionnaire_responses
        WHERE major IS NOT NULL AND major != ''
        GROUP BY major
        ORDER BY count DESC
        LIMIT 20
      `);
      const majorResults = await majorStmt.all();

      // 获取毕业年份分布
      const graduationYearStmt = this.db.prepare(`
        SELECT graduation_year as name, COUNT(*) as count
        FROM questionnaire_responses
        WHERE graduation_year IS NOT NULL AND graduation_year != ''
        GROUP BY graduation_year
        ORDER BY graduation_year DESC
      `);
      const graduationYearResults = await graduationYearStmt.all();

      // 计算百分比
      const calculatePercentage = (results, total) => {
        return results.map(item => ({
          ...item,
          percentage: total > 0 ? Math.round((item.count / total) * 100) : 0
        }));
      };

      const statistics = {
        total,
        educationLevels: calculatePercentage(educationResults.results || [], total),
        regions: calculatePercentage(regionResults.results || [], total),
        employmentStatus: calculatePercentage(employmentResults.results || [], total),
        majors: calculatePercentage(majorResults.results || [], total),
        graduationYears: calculatePercentage(graduationYearResults.results || [], total)
      };
      
      console.log('[DB] 统计数据获取成功:', statistics);
      
      return {
        success: true,
        data: statistics
      };
    } catch (error) {
      console.error('[DB] 获取统计数据失败:', error);
      return {
        success: false,
        error: error.message,
        data: {
          total: 0,
          educationLevels: [],
          regions: [],
          employmentStatus: []
        }
      };
    }
  }

  /**
   * 获取待审核内容
   */
  async getPendingContent(limit = 50) {
    try {
      console.log('[DB] 获取待审核内容');
      
      const stmt = this.db.prepare(`
        SELECT * FROM stories 
        WHERE status = 'pending' 
        ORDER BY created_at DESC 
        LIMIT ?
      `);
      
      const result = await stmt.bind(limit).all();
      
      console.log(`[DB] 获取到 ${result.results?.length || 0} 条待审核内容`);
      
      return {
        success: true,
        data: result.results || []
      };
    } catch (error) {
      console.error('[DB] 获取待审核内容失败:', error);
      return {
        success: false,
        error: error.message,
        data: []
      };
    }
  }

  /**
   * 审核内容
   */
  async reviewContent(contentId, action, reviewerId, notes = '') {
    try {
      console.log(`[DB] 审核内容: ${contentId}, 动作: ${action}`);
      
      const status = action === 'approve' ? 'approved' : 'rejected';
      
      const stmt = this.db.prepare(`
        UPDATE stories 
        SET status = ?, reviewer_id = ?, reviewed_at = ?, review_notes = ?, updated_at = ?
        WHERE id = ?
      `);
      
      const result = await stmt.bind(
        status,
        reviewerId,
        new Date().toISOString(),
        notes,
        new Date().toISOString(),
        contentId
      ).run();
      
      // 记录审核日志
      const logStmt = this.db.prepare(`
        INSERT INTO review_logs (id, reviewer_id, content_id, action, review_notes, created_at)
        VALUES (?, ?, ?, ?, ?, ?)
      `);
      
      await logStmt.bind(
        generateId('log'),
        reviewerId,
        contentId,
        action,
        notes,
        new Date().toISOString()
      ).run();
      
      console.log(`[DB] 内容审核完成: ${contentId}`, result);
      
      return {
        success: true,
        data: {
          contentId,
          status,
          action,
          affectedRows: result.meta.changes
        }
      };
    } catch (error) {
      console.error('[DB] 审核内容失败:', error);
      return {
        success: false,
        error: error.message
      };
    }
  }

  /**
   * 获取故事列表
   */
  async getStoryList(params = {}) {
    try {
      const {
        page = 1,
        pageSize = 6,
        sortBy = 'latest',
        category,
        tag,
        search
      } = params;

      console.log('[DB] 获取故事列表:', params);

      // 构建查询条件
      let whereClause = "WHERE status = 'approved'";
      const queryParams = [];

      if (category) {
        whereClause += " AND category = ?";
        queryParams.push(category);
      }

      if (tag) {
        whereClause += " AND tags LIKE ?";
        queryParams.push(`%"${tag}"%`);
      }

      if (search) {
        whereClause += " AND (title LIKE ? OR content LIKE ?)";
        queryParams.push(`%${search}%`, `%${search}%`);
      }

      // 构建排序
      let orderClause = "ORDER BY created_at DESC";
      if (sortBy === 'popular') {
        orderClause = "ORDER BY likes DESC, created_at DESC";
      }

      // 计算偏移量
      const offset = (page - 1) * pageSize;

      // 获取故事列表
      const storiesQuery = `
        SELECT id, title, content, category, tags, likes, dislikes,
               created_at, updated_at, is_anonymous
        FROM stories
        ${whereClause}
        ${orderClause}
        LIMIT ? OFFSET ?
      `;

      const storiesStmt = this.db.prepare(storiesQuery);
      const storiesResult = await storiesStmt.bind(...queryParams, pageSize, offset).all();

      // 获取总数
      const countQuery = `SELECT COUNT(*) as total FROM stories ${whereClause}`;
      const countStmt = this.db.prepare(countQuery);
      const countResult = await countStmt.bind(...queryParams).first();

      // 处理故事数据
      const stories = (storiesResult.results || []).map(story => ({
        ...story,
        tags: story.tags ? JSON.parse(story.tags) : [],
        content_preview: story.content.substring(0, 200) + (story.content.length > 200 ? '...' : '')
      }));

      const total = countResult?.total || 0;

      console.log(`[DB] 故事列表获取成功: ${stories.length} 条记录, 总计: ${total}`);

      return {
        success: true,
        data: {
          stories,
          total
        }
      };
    } catch (error) {
      console.error('[DB] 获取故事列表失败:', error);
      return {
        success: false,
        error: error.message,
        data: {
          stories: [],
          total: 0
        }
      };
    }
  }

  /**
   * 获取故事详情
   */
  async getStoryDetail(storyId) {
    try {
      console.log(`[DB] 获取故事详情: ${storyId}`);

      const stmt = this.db.prepare(`
        SELECT id, title, content, category, tags, likes, dislikes,
               created_at, updated_at, is_anonymous
        FROM stories
        WHERE id = ? AND status = 'approved'
      `);

      const result = await stmt.bind(storyId).first();

      if (!result) {
        return {
          success: false,
          error: 'Story not found',
          data: null
        };
      }

      // 处理标签
      const story = {
        ...result,
        tags: result.tags ? JSON.parse(result.tags) : []
      };

      console.log(`[DB] 故事详情获取成功: ${storyId}`);

      return {
        success: true,
        data: story
      };
    } catch (error) {
      console.error('[DB] 获取故事详情失败:', error);
      return {
        success: false,
        error: error.message,
        data: null
      };
    }
  }

  /**
   * 故事投票
   */
  async voteStory(storyId, voteType) {
    try {
      console.log(`[DB] 故事投票: ${storyId}, 类型: ${voteType}`);

      const column = voteType === 'like' ? 'likes' : 'dislikes';

      const stmt = this.db.prepare(`
        UPDATE stories
        SET ${column} = ${column} + 1, updated_at = ?
        WHERE id = ? AND status = 'approved'
      `);

      const result = await stmt.bind(new Date().toISOString(), storyId).run();

      if (result.meta.changes === 0) {
        return {
          success: false,
          error: 'Story not found or not approved'
        };
      }

      // 获取更新后的投票数
      const getStmt = this.db.prepare(`
        SELECT likes, dislikes FROM stories WHERE id = ?
      `);
      const storyResult = await getStmt.bind(storyId).first();

      console.log(`[DB] 故事投票成功: ${storyId}`);

      return {
        success: true,
        data: {
          storyId,
          likes: storyResult?.likes || 0,
          dislikes: storyResult?.dislikes || 0,
          voteType
        }
      };
    } catch (error) {
      console.error('[DB] 故事投票失败:', error);
      return {
        success: false,
        error: error.message
      };
    }
  }

  /**
   * 健康检查
   */
  async healthCheck() {
    try {
      const result = await this.db.prepare('SELECT 1 as test').first();
      return {
        success: true,
        connected: !!result,
        timestamp: new Date().toISOString()
      };
    } catch (error) {
      console.error('[DB] 健康检查失败:', error);
      return {
        success: false,
        connected: false,
        error: error.message,
        timestamp: new Date().toISOString()
      };
    }
  }
}

export default DatabaseService;
